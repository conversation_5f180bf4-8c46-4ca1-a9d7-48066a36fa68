import { authenticateAdminRequest } from '@/lib/admin-auth'
import { supabase } from '@/lib/supabase'

// Define a name for the exceptions table, assuming it might be different
// from the direct guess. If it's standard, this can be simplified.
const ARTIST_AVAILABILITY_EXCEPTIONS_TABLE = 'artist_availability_exceptions';

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(`[${requestId}] /api/artist/availability/exceptions called, method: ${req.method}`);

  try {
    // Authenticate the request using the admin auth system
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const { user, role } = authResult

    const allowedRoles = ['artist', 'braider', 'admin', 'dev']; // Admin/dev might manage exceptions too
    if (!allowedRoles.includes(role)) {
      console.warn(`[${requestId}] User ${user.id} with role '${role}' attempted to access exceptions. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You do not have permission to access this resource.' });
    }

    // Get artist_id from artist_profiles table using user.id
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (profileError || !artistProfile) {
      console.error(`[${requestId}] Error fetching artist profile for user ${user.id}:`, profileError);
      // If an admin is making the call, they might provide artist_id in query/body
      // For now, strictly for the authenticated artist
      return res.status(404).json({ error: 'Artist profile not found for the authenticated user.' });
    }
    const artistId = artistProfile.id;

    if (req.method === 'GET') {
      console.log(`[${requestId}] Fetching exceptions for artist_id: ${artistId}`);
      const { startDate, endDate } = req.query; // Optional query parameters for date range

      let query = supabase
        .from(ARTIST_AVAILABILITY_EXCEPTIONS_TABLE)
        .select('*')
        .eq('artist_id', artistId);

      if (startDate) {
        query = query.gte('exception_date', startDate);
      }
      if (endDate) {
        query = query.lte('exception_date', endDate);
      }
      query = query.order('exception_date', { ascending: true });

      const { data: exceptions, error } = await query;

      if (error) {
        // Check if the error is because the table doesn't exist (PostgREST error code 42P01)
        if (error.code === '42P01') {
             console.warn(`[${requestId}] Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Returning empty array.`);
             return res.status(200).json([]); // Return empty array if table doesn't exist
        }
        console.error(`[${requestId}] Error fetching exceptions for artist ${artistId}:`, error);
        return res.status(500).json({ error: 'Failed to fetch exceptions', details: error.message });
      }

      console.log(`[${requestId}] Exceptions fetched successfully for artist ${artistId}:`, exceptions);
      return res.status(200).json(exceptions || []); // Ensure an array is always returned
    }
    else if (req.method === 'POST') {
      const { exception_date, exception_type, start_time, end_time, notes } = req.body;
      console.log(`[${requestId}] Creating new exception for artist_id: ${artistId}`, req.body);

      // Basic Validation
      if (!exception_date || !exception_type) {
        return res.status(400).json({ error: 'Exception date and type are required.' });
      }
      const validTypes = ['Unavailable', 'Custom Hours', 'Additional Break'];
      if (!validTypes.includes(exception_type)) {
        return res.status(400).json({ error: 'Invalid exception type.' });
      }
      if ((exception_type === 'Custom Hours' || exception_type === 'Additional Break') && (!start_time || !end_time)) {
        return res.status(400).json({ error: 'Start time and end time are required for Custom Hours or Additional Break.' });
      }
      // TODO: Add more robust date and time format validation

      const newException = {
        artist_id: artistId,
        exception_date,
        exception_type,
        start_time: (exception_type === 'Custom Hours' || exception_type === 'Additional Break') ? start_time : null,
        end_time: (exception_type === 'Custom Hours' || exception_type === 'Additional Break') ? end_time : null,
        notes: notes || null,
      };

      const { data: createdException, error } = await supabase
        .from(ARTIST_AVAILABILITY_EXCEPTIONS_TABLE)
        .insert(newException)
        .select()
        .single();

      if (error) {
         // Check if the error is because the table doesn't exist (PostgREST error code 42P01)
        if (error.code === '42P01') {
            console.error(`[${requestId}] Error creating exception: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Please ensure migrations are run.`);
            return res.status(500).json({ error: `Database configuration error: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Please contact support.` });
        }
        console.error(`[${requestId}] Error creating exception for artist ${artistId}:`, error);
        return res.status(500).json({ error: 'Failed to create exception.', details: error.message });
      }

      console.log(`[${requestId}] Exception created successfully for artist ${artistId}:`, createdException);
      return res.status(201).json(createdException);
    } else {
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in /api/artist/availability/exceptions:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
