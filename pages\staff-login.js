import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import Layout from '@/components/Layout'
import styles from '@/styles/StaffLogin.module.css'

export default function StaffLogin() {
  const router = useRouter()
  const { signIn, user, role, loading: authLoading } = useAuth(); // Added role here
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Check if user is already authenticated and redirect appropriately
  useEffect(() => {
    console.log('[StaffLogin useEffect] Running. AuthLoading:', authLoading, 'User:', user?.email, 'Role:', role, 'RouterReady:', router.isReady);
    // Only attempt redirection logic on the client-side, when router is ready,
    // and auth state is no longer loading.
    if (typeof window !== 'undefined' && router.isReady && !authLoading) {
      console.log('[StaffLogin useEffect] Conditions met for further checks. User:', user?.email, 'Role:', role);
      if (user && typeof role === 'string' && role) { // Check user, and role is a defined non-empty string
        let returnUrl = router.query.redirect;
        if (!returnUrl) {
          if (role === 'artist' || role === 'braider') {
            // TODO: Later, check onboarding status
            returnUrl = '/admin/artist-braider-dashboard';
          } else if (role === 'admin' || role === 'dev') {
            returnUrl = '/admin';
          } else {
            returnUrl = '/'; // Fallback for unexpected (but defined) roles
            console.warn(`[StaffLogin useEffect] User authenticated with unexpected role (${role}), redirecting to fallback: ${returnUrl}`);
          }
        }
        console.log(`[StaffLogin useEffect] Attempting redirect to: ${returnUrl}, Current query: ${JSON.stringify(router.query)}`);
        router.push(returnUrl);
      } else if (user && (role === null || typeof role === 'undefined')) {
          // User object exists but role is not yet determined or is null.
          // This might be a transient state. Avoid redirecting to prevent errors.
          // AuthContext should eventually update role.
          console.log('[StaffLogin useEffect] User authenticated but role not yet determined. Waiting for role update.');
      } else {
        // If !user (not authenticated), or other conditions not met for redirect
        console.log('[StaffLogin useEffect] Conditions for redirect NOT met, login form should render or waiting for role.');
      }
    } else {
      console.log('[StaffLogin useEffect] Initial conditions (client-side, router ready, not auth loading) NOT met.');
    }
  }, [user, role, authLoading, router, router.isReady, router.query.redirect]); // Added router.isReady and router.query.redirect

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { email, password } = formData
    console.log(`[StaffLogin handleSubmit] Attempting login for: ${email}`);

    // Basic validation
    if (!email || !password) {
      console.log('[StaffLogin handleSubmit] Validation failed: Email or password missing.');
      setError('Please enter both email and password')
      setLoading(false)
      return
    }

    // console.log('[Staff Login] Starting authentication for:', email) // Original log

    try {
      // Sign in with email and password
      // console.log('[Staff Login] Calling signIn function...') // Original log
      const result = await signIn(email, password)
      console.log('[StaffLogin handleSubmit] signIn result:', result ? { user: result.data?.user?.email, role: result.data?.role, error: result.error?.message } : 'null result');

      // console.log('[Staff Login] SignIn result:', { // Original log
      //   hasData: !!result.data,
      //   hasError: !!result.error,
      //   errorMessage: result.error?.message
      // })

      // Comprehensive check for user data and role before checking result.error
      if (!result || !result.data || !result.data.user || !result.data.role) {
        const reason = !result ? 'Result is null' : (!result.data ? 'result.data is null' : (!result.data.user ? 'result.data.user is null' : 'result.data.role is null'));
        console.error(`[StaffLogin handleSubmit] Authentication response missing user data or role. Reason: ${reason}. Result:`, result);
        setError('Authentication failed: User information is incomplete. Please contact support.');
        toast.error('Login failed: Incomplete user data received.');
        setLoading(false);
        return;
      }

      // Now, handle explicit errors from the signIn function (e.g., network issues, Supabase errors)
      if (result.error) {
        console.error('[StaffLogin handleSubmit] Authentication error from signIn:', result.error);
        setError(result.error.message || 'Authentication failed. Please check your credentials.')
        toast.error('Login failed. Please check your credentials and try again.')
        setLoading(false)
        return
      }

      // At this point, we have result.data.user and result.data.role
      const userRole = result.data.role; // Define userRole for logging
      // Explicitly check the role against allowed staff roles
      const allowedStaffRoles = ['artist', 'braider', 'admin', 'dev'];
      if (!allowedStaffRoles.includes(userRole)) {
        console.warn(`[StaffLogin handleSubmit] User ${result.data.user.email} authenticated with an unsupported role for staff portal: ${userRole}`);
        setError(`Access denied: Your role (${userRole}) is not permitted for the staff portal. Please contact support if you believe this is an error.`);
        toast.error('Login failed: Insufficient permissions.');
        setLoading(false);
        // Optionally, sign the user out if they were technically logged in with an invalid role for this portal
        // await signOut(); // If signOut is available in this context, or manage through AuthContext
        return;
      }

      // All checks passed, authentication is successful and role is appropriate
      // console.log(`[Staff Login] ✅ Authentication successful for ${result.data.user.email} with role: ${result.data.role}`) // Original log

      // Show success message
      toast.success('Welcome back!')
      setLoading(false) // Ensure loading is false on success before redirect

      // Determine redirect URL based on role
      let redirectUrl = router.query.redirect; // Use query param if present
      if (!redirectUrl) { // If no query param, determine by role
        // const userRole = result.data.role; // Already defined above
        if (userRole === 'artist' || userRole === 'braider') {
          // TODO: Later, check onboarding status and redirect to /artist/my-profile if not complete
          redirectUrl = '/admin/artist-braider-dashboard'; // Or the correct artist dashboard path
        } else if (userRole === 'admin' || userRole === 'dev') {
          redirectUrl = '/admin'; // Main admin dashboard
        } else {
          redirectUrl = '/'; // Fallback for unexpected roles, or an error page
          console.warn(`[StaffLogin handleSubmit] Unknown role for redirection: ${userRole}`);
        }
      }

      console.log(`[StaffLogin handleSubmit] Login successful. Role: ${userRole}. Redirecting to: ${redirectUrl}`);
      router.push(redirectUrl);
      // The useEffect hook that watches `user` changes will also attempt a redirect.
      // This direct push ensures the role-specific one happens immediately.
      // The useEffect can serve as a backup or for general session validation on page loads.

    } catch (err) {
      console.error('[StaffLogin handleSubmit] Unexpected error during authentication:', err.message, err.stack)
      setError('An unexpected error occurred. Please try again.')
      toast.error('Login failed. Please try again later.')
      setLoading(false)
    }
  }

  // Show loading while checking auth OR if user is set and router is ready (implying redirect is about to happen)
  if (authLoading || (user && router.isReady)) {
    return (
      <Layout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading...</p>
        </div>
      </Layout>
    );
  }
  // If we reach here, it means !authLoading AND (!user OR !router.isReady).
  // If !user, the form should render. If user exists but router not ready, form might briefly flash,
  // but the useEffect will catch it once router is ready.

  return (
    <Layout>
      <Head>
        <title>Staff Login | Ocean Soul Sparkles</title>
        <meta name="description" content="Staff login portal for Ocean Soul Sparkles team members, artists, and administrators." />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <main className={styles.main}>
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.logo}>
              <h1>Ocean Soul Sparkles</h1>
              <div className={styles.subtitle}>Staff Portal</div>
            </div>
            <p className={styles.description}>
              Welcome back! Please sign in to access your staff dashboard.
            </p>
          </div>

          <div className={styles.formWrapper}>
            <form onSubmit={handleSubmit} className={styles.form}>
              <div className={styles.formGroup}>
                <label htmlFor="email" className={styles.label}>
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={styles.input}
                  placeholder="Enter your work email"
                  required
                  autoComplete="email"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="password" className={styles.label}>
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={styles.input}
                  placeholder="Enter your password"
                  required
                  autoComplete="current-password"
                />
              </div>

              {error && (
                <div className={styles.error}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                  </svg>
                  {error}
                </div>
              )}

              <button
                type="submit"
                className={styles.submitButton}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className={styles.buttonSpinner}></div>
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>
          </div>

          <div className={styles.footer}>
            <div className={styles.helpText}>
              <p>
                <strong>For Staff Members:</strong> Use the credentials provided by your administrator.
              </p>
              <p>
                <strong>Need Help?</strong> Contact your administrator or email{' '}
                <a href="mailto:<EMAIL>" className={styles.link}>
                  <EMAIL>
                </a>
              </p>
            </div>

            <div className={styles.publicLinks}>
              <Link href="/" className={styles.link}>
                ← Back to Main Website
              </Link>
              <span className={styles.separator}>|</span>
              <Link href="/login" className={styles.link}>
                Customer Login
              </Link>
            </div>
          </div>
        </div>
      </main>
    </Layout>
  )
}
