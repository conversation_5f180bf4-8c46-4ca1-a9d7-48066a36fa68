import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import AdminLayout from '@/components/admin/AdminLayout';
import ProfileManagementCard from '@/components/admin/ProfileManagementCard';
import AvailabilitySettings from '@/components/admin/AvailabilitySettings'; // Import AvailabilitySettings
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useState, useEffect } from 'react'; // Import useState and useEffect
import styles from '@/styles/admin/MyProfilePage.module.css'; // Create this CSS module

const MyProfilePage = () => {
  const { user, loading: authLoading, supabaseClient, fetchUserProfile } = useAuth();
  const router = useRouter();

  // State for artist profile data specific to AvailabilitySettings
  const [artistProfileData, setArtistProfileData] = useState(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState(null);

  useEffect(() => {
    const fetchArtistProfile = async () => {
      if (user?.id && supabaseClient) {
        setProfileLoading(true);
        setProfileError(null);
        try {
          const { data, error } = await supabaseClient
            .from('artist_profiles')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
            throw error;
          }
          setArtistProfileData(data);
        } catch (err) {
          console.error('Error fetching artist profile for settings:', err);
          setProfileError('Failed to load artist profile data.');
          toast.error('Failed to load artist profile data for availability.');
        } finally {
          setProfileLoading(false);
        }
      } else if (!user?.id) {
        setProfileLoading(false); // Not logged in, nothing to load
      }
    };

    fetchArtistProfile();
  }, [user?.id, supabaseClient]);

  const handleProfileUpdateSuccess = async (updatedProfileByCard) => {
    toast.success('Profile updated successfully!');
    // Re-fetch artist profile data to update AvailabilitySettings if needed
    if (user?.id && supabaseClient) {
      setProfileLoading(true);
      try {
        const { data, error } = await supabaseClient
          .from('artist_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();
        if (error && error.code !== 'PGRST116') throw error;
        setArtistProfileData(data);
      } catch (err) {
        toast.error('Could not refresh profile data for availability settings.');
      } finally {
        setProfileLoading(false);
      }
    }
    // Optionally, refresh user data in AuthContext
    if (fetchUserProfile) {
      await fetchUserProfile();
    }
    // router.push('/admin/artist-braider-dashboard'); // Keep or remove based on desired UX
  };

  const handleProfileUpdateError = (errorMessage) => {
    toast.error(errorMessage || 'Failed to update profile. Please try again.');
  };

  if (authLoading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading your profile...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    // This should ideally be caught by ProtectedRoute, but as a fallback
    router.push('/staff-login?redirect=/admin/my-profile');
    return (
        <AdminLayout>
            <div className={styles.loadingContainer}><p>Redirecting to login...</p></div>
        </AdminLayout>
    );
  }

  if (!user.artistProfile && !authLoading) {
    // If user is loaded but artistProfile is not (maybe a new user who hasn't completed profile yet)
    // This page assumes a profile exists to be managed.
    // The complete-profile page is for initial setup.
    // However, ProfileManagementCard can handle upsert, so it might still be usable.
    // For now, let's ensure they have an artistProfile record.
    // This check might be redundant if dashboard already redirects to complete-profile.
    toast.warn('Profile data not fully loaded. You might need to complete initial setup.');
    // router.push('/admin/complete-profile'); // Consider this if profile MUST be complete first
    // return <AdminLayout><div className={styles.loadingContainer}><p>Loading profile data...</p></div></AdminLayout>;
  }

  // The artistId for ProfileManagementCard should be user.id (auth.users.id)
  // as ProfileManagementCard uses user.id to fetch/update its specific artist_profiles record.
  // Or, if ProfileManagementCard expects the artist_profiles primary key, ensure it's passed correctly.
  // Based on previous ProfileManagementCard usage, it expects 'artistId' which is user.id (auth.users.id).

  return (
    <ProtectedRoute allowedRoles={['artist', 'braider', 'admin', 'dev']}> {/* Allow admin/dev to see structure if needed */}
      <AdminLayout>
        <Head>
          <title>My Profile | Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.container}>
          <h1 className={styles.title}>Edit Your Profile</h1>
          <p className={styles.description}>
            Keep your information up to date. Changes will be reflected across the platform.
          </p>

          {user && user.id ? (
            <>
              <div className={styles.section}>
                <h2 className={styles.sectionTitle}>Your Details</h2>
                <ProfileManagementCard
                  artistId={user.id}
                  onProfileUpdate={handleProfileUpdateSuccess}
                  onProfileError={handleProfileUpdateError}
                />
              </div>

              <div className={styles.section}>
                <h2 className={styles.sectionTitle}>Manage Availability</h2>
                {profileLoading && <div className={styles.loadingContainer}><div className={styles.spinner}></div><p>Loading availability settings...</p></div>}
                {profileError && <p className={styles.errorText}>{profileError}</p>}
                {!profileLoading && !profileError && artistProfileData && user?.id && (
                  <AvailabilitySettings artistProfile={artistProfileData} />
                )}
                {!profileLoading && !profileError && !artistProfileData && user?.id && (
                  <p>Complete your main profile above to manage availability. If you've just created your profile, this section will update shortly.</p>
                )}
                 {!profileLoading && !user?.id && (
                  <p>User information not available. Cannot load availability settings.</p>
                )}
              </div>
            </>
          ) : (
            !authLoading && <p>User information not available. Please log in again.</p> // Show if user is null and not authLoading
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default MyProfilePage;
