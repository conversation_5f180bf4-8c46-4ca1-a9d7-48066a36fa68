import { authenticateAdminRequest } from '@/lib/admin-auth'
import { supabase } from '@/lib/supabase'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(`[${requestId}] /api/artist/availability/schedule called, method: ${req.method}`);

  try {
    // Authenticate the request using the admin auth system
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const { user, role } = authResult

    const allowedRoles = ['artist', 'braider', 'admin', 'dev']; // Admin/dev might manage schedules too
    if (!allowedRoles.includes(role)) {
      console.warn(`[${requestId}] User ${user.id} with role '${role}' attempted to access schedule. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You do not have permission to access this resource.' });
    }

    // Get artist_id from artist_profiles table using user.id
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (profileError || !artistProfile) {
      console.error(`[${requestId}] Error fetching artist profile for user ${user.id}:`, profileError);
      return res.status(404).json({ error: 'Artist profile not found.' });
    }
    const artistId = artistProfile.id;

    if (req.method === 'GET') {
      console.log(`[${requestId}] Fetching schedule for artist_id: ${artistId}`);
      const { data: schedule, error } = await supabase
        .from('artist_availability_schedule')
        .select('*')
        .eq('artist_id', artistId)
        .order('day_of_week', { ascending: true });

      if (error) {
        console.error(`[${requestId}] Error fetching schedule for artist ${artistId}:`, error);
        return res.status(500).json({ error: 'Failed to fetch schedule', details: error.message });
      }

      console.log(`[${requestId}] Schedule fetched successfully for artist ${artistId}:`, schedule);
      return res.status(200).json(schedule);

    } else if (req.method === 'POST') {
      const newSchedule = req.body; // Expects an array of schedule objects
      console.log(`[${requestId}] Updating schedule for artist_id: ${artistId}`, newSchedule);

      if (!Array.isArray(newSchedule)) {
        return res.status(400).json({ error: 'Invalid request body: Expected an array of schedule items.' });
      }

      // Validate each schedule item
      for (const item of newSchedule) {
        if (item.day_of_week === undefined || item.day_of_week < 0 || item.day_of_week > 6) {
          return res.status(400).json({ error: 'Invalid day_of_week in schedule item.' });
        }
        if (item.is_available && (!item.start_time || !item.end_time)) {
          return res.status(400).json({ error: `Start time and end time are required for available days. Day: ${item.day_of_week}` });
        }
        // Add more specific time format validation if necessary
      }

      // Perform operations in a transaction-like manner (delete old, insert new)
      // Supabase client library doesn't directly support transactions in this way for multiple operations.
      // We'll do it sequentially and rely on error handling.
      // For more complex scenarios, a database function/procedure would be better.

      console.log(`[${requestId}] Deleting existing schedule for artist_id: ${artistId}`);
      const { error: deleteError } = await supabase
        .from('artist_availability_schedule')
        .delete()
        .eq('artist_id', artistId);

      if (deleteError) {
        console.error(`[${requestId}] Error deleting old schedule for artist ${artistId}:`, deleteError);
        return res.status(500).json({ error: 'Failed to update schedule (delete phase).', details: deleteError.message });
      }
      console.log(`[${requestId}] Old schedule deleted successfully for artist ${artistId}.`);

      if (newSchedule.length > 0) {
        const scheduleToInsert = newSchedule.map(item => ({
          artist_id: artistId,
          day_of_week: item.day_of_week,
          start_time: item.is_available ? item.start_time : null,
          end_time: item.is_available ? item.end_time : null,
          is_available: item.is_available,
          break_start_time: item.is_available && item.break_start_time ? item.break_start_time : null,
          break_end_time: item.is_available && item.break_end_time ? item.break_end_time : null,
          notes: item.notes || null,
        }));

        console.log(`[${requestId}] Inserting new schedule for artist_id: ${artistId}`, scheduleToInsert);
        const { data: insertedSchedule, error: insertError } = await supabase
          .from('artist_availability_schedule')
          .insert(scheduleToInsert)
          .select();

        if (insertError) {
          console.error(`[${requestId}] Error inserting new schedule for artist ${artistId}:`, insertError);
          return res.status(500).json({ error: 'Failed to update schedule (insert phase).', details: insertError.message });
        }
        console.log(`[${requestId}] New schedule inserted successfully for artist ${artistId}:`, insertedSchedule);
        return res.status(200).json(insertedSchedule);
      } else {
        // If newSchedule is empty, it means all availability was cleared.
        console.log(`[${requestId}] New schedule is empty. All availability cleared for artist ${artistId}.`);
        return res.status(200).json([]);
      }

    } else {
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in /api/artist/availability/schedule:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
